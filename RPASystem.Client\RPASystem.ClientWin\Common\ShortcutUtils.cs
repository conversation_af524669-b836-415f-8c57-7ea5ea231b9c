using System;
using System.IO;
using System.Windows.Forms;

namespace RPASystem.Client.Common
{
    /// <summary>
    /// 快捷方式工具类
    /// </summary>
    public static class ShortcutUtils
    {
        /// <summary>
        /// 在桌面创建应用程序快捷方式
        /// </summary>
        /// <param name="shortcutName">快捷方式名称，不包含.lnk扩展名</param>
        /// <param name="description">快捷方式描述</param>
        /// <returns>是否成功创建</returns>
        public static bool CreateDesktopShortcut(string shortcutName = "RPA大助手", string description = "RPA大助手客户端")
        {
            try
            {
                // 获取桌面路径
                string desktopPath = Environment.GetFolderPath(Environment.SpecialFolder.Desktop);
                // 快捷方式文件路径
                string shortcutPath = Path.Combine(desktopPath, shortcutName + ".lnk");
                string shortcutExePath = Path.Combine(desktopPath, shortcutName + ".exe");

                // 如果快捷方式已存在，则不重复创建
                if (File.Exists(shortcutPath) || File.Exists(shortcutExePath))
                    return true;

                // 获取应用程序路径
                string appPath = Application.ExecutablePath;

                // 创建WshShell对象
                Type shellType = Type.GetTypeFromProgID("WScript.Shell");
                dynamic shell = Activator.CreateInstance(shellType);

                // 创建快捷方式对象
                dynamic shortcut = shell.CreateShortcut(shortcutPath);

                // 设置快捷方式属性
                shortcut.TargetPath = appPath;
                shortcut.WorkingDirectory = Path.GetDirectoryName(appPath);
                shortcut.Description = description;
                shortcut.IconLocation = appPath + ",0"; // 使用应用程序图标

                // 保存快捷方式
                shortcut.Save();

                return true;
            }
            catch (Exception ex)
            {
                // 创建快捷方式失败，记录错误但不影响程序运行
                Console.WriteLine("创建桌面快捷方式失败: " + ex.Message);
                return false;
            }
        }
    }
}